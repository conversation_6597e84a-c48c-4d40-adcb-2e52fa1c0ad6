package logger

import (
	"sync"

	"go.uber.org/zap"
)

var (
	logger *zap.Logger
	once   sync.Once
)

// Initialize initializes the global logger
func Initialize() {
	once.Do(func() {
		var err error
		logger, err = zap.NewProduction() // Use zap.NewDevelopment() for dev environments
		if err != nil {
			panic("Failed to initialize logger: " + err.Error())
		}
	})
}

// GetLogger returns the global logger instance
func GetLogger() *zap.Logger {
	if logger == nil {
		Initialize()
	}
	return logger
}
