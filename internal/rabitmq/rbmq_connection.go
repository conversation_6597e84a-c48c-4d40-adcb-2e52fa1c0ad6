package rabitmq

import (
	"context"
	"errors"
	"log"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	amqp "github.com/rabbitmq/amqp091-go"
)

type RabbitBackground struct {
	connString    string
	connection    *amqp.Connection
	mu            sync.RWMutex
	shutdownChan  chan struct{}
	ready         chan struct{} // Signals initial connection
	IsReconnected chan struct{} // Signals reconnection
}

func NewRabbitBackground(connString string) *RabbitBackground {
	rb := &RabbitBackground{
		connString:    connString,
		shutdownChan:  make(chan struct{}),
		ready:         make(chan struct{}),
		IsReconnected: make(chan struct{}),
	}

	go rb.startConnectionLoop()
	return rb
}

// GetConnection waits for initial connection and returns valid connection
func (rb *RabbitBackground) GetConnection(ctx context.Context) (*amqp.Connection, error) {
	// Wait for initial connection or timeout
	select {
	case <-rb.ready:
		// Continue to check connection status
	case <-ctx.Done():
		return nil, errors.New("connection wait canceled")
	case <-time.After(5 * time.Second):
		return nil, errors.New("connection timeout")
	}

	rb.mu.RLock()
	defer rb.mu.RUnlock()

	if rb.connection == nil || rb.connection.IsClosed() {
		return nil, errors.New("connection not available")
	}

	return rb.connection, nil
}

func (rb *RabbitBackground) startConnectionLoop() {
	bo := backoff.NewExponentialBackOff()
	bo.MaxInterval = 30 * time.Second
	bo.MaxElapsedTime = 0

	var initialConnection bool

	for {
		select {
		case <-rb.shutdownChan:
			return
		default:
			log.Printf("Attempting to connect to RabbitMQ at %s", rb.connString)

			conn, err := amqp.Dial(rb.connString)
			if err != nil {
				log.Printf("Connection failed: %v (retrying)", err)
				time.Sleep(bo.NextBackOff())
				continue
			}

			rb.mu.Lock()
			if rb.connection != nil {
				rb.connection.Close()
			}
			rb.connection = conn

			// Signal readiness on first successful connection
			if !initialConnection {
				close(rb.ready)
				initialConnection = true
			}
			rb.mu.Unlock()

			log.Println("Successfully connected to RabbitMQ")
			rb.IsReconnected <- struct{}{}

			closeChan := make(chan *amqp.Error, 1)
			conn.NotifyClose(closeChan)

			select {
			case err := <-closeChan:
				log.Printf("Connection lost: %v (reconnecting)", err)
			case <-rb.shutdownChan:
				conn.Close()
				return
			}
		}
	}
}

// IsReady checks if initial connection has been established
func (rb *RabbitBackground) IsReady() bool {
	select {
	case <-rb.ready:
		return true
	default:
		return false
	}
}

func (rb *RabbitBackground) Shutdown() {
	close(rb.shutdownChan)
	rb.mu.Lock()
	defer rb.mu.Unlock()
	if rb.connection != nil {
		rb.connection.Close()
	}
}
