package gamecore

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"runtime"
	"smartrpc/logger"
	"smartrpc/pkg"
	"sort"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
	"github.com/k0kubun/pp/v3"
	"go.uber.org/zap"
)

type BetsType struct {
	Type     int     `db:"type"`
	TotalPay float64 `db:"total_pay"`
}

type Setting struct {
	LowestPayout  int `db:"lowest_payout" json:"lowest_payout"`
	HighestPayout int `db:"highest_payout" json:"highest_payout"`
	LowPay        int `db:"low_pay" json:"low_pay"`
	Natural       int `db:"natural" json:"natural"`
	Margin        int `db:"margin" json:"margin"`
}

type TotalReport struct {
	TurnOver float64 `db:"turn_over"`
	WinLose  float64 `db:"win_lose"`
}
type SystemSetting struct {
	ID       float64 `db:"id" json:"id"`
	Settings string  `db:"settings" json:"settings"`
}

func GetKeno5dResult(minSum, maxSum int) []int {
	resChan := make(chan []int)

	for i := 0; i < runtime.NumCPU(); i++ {
		go func() {
			for {
				select {
				case <-time.After(2 * time.Second):
					return
				default:
					data := pkg.PickUniqueNumbers()
					if pkg.SumArrayInt(data) >= minSum && pkg.SumArrayInt(data) <= maxSum {
						resChan <- data
					}
				}
			}
		}()
	}

	select {
	case result := <-resChan:
		return result
	case <-time.After(2 * time.Second):
		// this condition is for when we can't fine the number after 2sec
		return pkg.GenerateSuffledNumbers(80)[:20]
	}

}


func chooseBetTypeStrategy(betTypes []BetsType, setting Setting) int {
	logger := logger.GetLogger()
	sugar := logger.Sugar()
	// Generate random number between 0 and 1
	probability := rand.Float64()
	sugar.Infof("Random probability: %.4f\n", probability)

	if probability <= float64(setting.HighestPayout)/100 { // chance to pick highest total pay bet type
		maxTotalPay := betTypes[0].TotalPay
		maxType := betTypes[0].Type
		for _, betType := range betTypes {
			if betType.TotalPay > maxTotalPay {
				maxTotalPay = betType.TotalPay
				maxType = betType.Type
			}
		}
		sugar.Infof("Choice 1 (%.2f): Highest total pay - Type: %d, Pay: %.2f\n", float64(setting.HighestPayout)/100, maxType, maxTotalPay)
		return maxType
	} else if probability <= float64(setting.HighestPayout+setting.LowPay)/100 { // chance to pick low pay bet type
		// Sort the betTypes by TotalPay
		sort.Slice(betTypes, func(i, j int) bool {
			return betTypes[i].TotalPay < betTypes[j].TotalPay
		})

		// Filter out bet types with 0 payout
		nonZeroBetTypes := []BetsType{}
		for _, betType := range betTypes {
			if betType.TotalPay > 0 {
				nonZeroBetTypes = append(nonZeroBetTypes, betType)
			}
		}

		var mediumPay float64
		var mediumType int
		n := len(nonZeroBetTypes)

		if n == 0 {
			// If all bet types have 0 payout, fallback to natural choice
			sugar.Error("All bet types have 0 payout")
			return 0
		}

		if n%2 == 1 { // Odd number of elements
			mediumPay = nonZeroBetTypes[n/2].TotalPay
			mediumType = nonZeroBetTypes[n/2].Type
		} else { // Even number of elements
			mediumPay = (nonZeroBetTypes[n/2-1].TotalPay + nonZeroBetTypes[n/2].TotalPay) / 2
			mediumType = nonZeroBetTypes[n/2-1].Type // or nonZeroBetTypes[n/2].Type based on your preference
		}

		sugar.Infof("Medium pay - Type: %d, Pay: %.2f\n", mediumType, mediumPay)
		return mediumType
	} else if probability <= float64(setting.HighestPayout+setting.LowPay+setting.LowestPayout)/100 { // chance to pick lowest total pay bet type
		minTotalPay := betTypes[0].TotalPay
		minType := betTypes[0].Type
		for _, betType := range betTypes {
			if betType.TotalPay < minTotalPay {
				minTotalPay = betType.TotalPay
				minType = betType.Type
			}
		}
		sugar.Infof("Choice 2 (%.2f): Lowest total pay - Type: %d, Pay: %.2f\n", float64(setting.LowestPayout)/100, minType, minTotalPay)
		return minType
	} else { // Natural choice
		sugar.Infof("Choice 3: Natural choice: %.2f,\n", float64(setting.Natural)/100)
		return 0
	}
}

func GetSetting(db *sqlx.DB, sugar *zap.SugaredLogger) Setting {
	sql := `
		SELECT * from tbl_smart_result_settings where id = 1
	`
	var setting SystemSetting
	err := db.Get(&setting, sql)
	if err != nil {
		sugar.Error(err)
	}
	var settings Setting
	err = json.Unmarshal([]byte(setting.Settings), &settings)
	if err != nil {
		sugar.Error(err)
	}
	return settings

}

func SystemDecision(round string, db *sqlx.DB, rdb *redis.Client) []int {
	logger := logger.GetLogger()
	sugar := logger.Sugar()

	setting := GetSetting(db, sugar)

	// ==============Return early if there is no bet=================
	betSql := `
		SELECT
			EXISTS (
				SELECT 1
				FROM tbl_bets
				WHERE round_uuid = $1
			)
	`

	var betExists bool
	err := db.Get(&betExists, betSql, round)
	if err != nil {
		sugar.Error("Failed to check bet existence", zap.Error(err))
		return rarityResult()
	}
	if !betExists {
		fmt.Println("================")
		fmt.Println("||   No bet   ||")
		fmt.Println("================")
		return rarityResult()
		// return pkg.GenerateNaturalNumbers()
	}
	// ============================================================

	sql := `
	WITH aggregated_bets AS (
		SELECT
			SUM(CASE WHEN tb.bet_type_id = ANY(ARRAY[1, 3]) THEN (tb.valid_bet * dr.rate) * tb.odds ELSE 0 END) AS sum_1,
			SUM(CASE WHEN tb.bet_type_id = ANY(ARRAY[1, 4]) THEN (tb.valid_bet * dr.rate) * tb.odds ELSE 0 END) AS sum_2,
			SUM(CASE WHEN tb.bet_type_id = ANY(ARRAY[1, 5]) THEN (tb.valid_bet * dr.rate) * tb.odds ELSE 0 END) AS sum_3,
			SUM(CASE WHEN tb.bet_type_id = ANY(ARRAY[2, 5]) THEN (tb.valid_bet * dr.rate) * tb.odds ELSE 0 END) AS sum_4,
			SUM(CASE WHEN tb.bet_type_id = ANY(ARRAY[2, 6]) THEN (tb.valid_bet * dr.rate) * tb.odds ELSE 0 END) AS sum_5,
			SUM(CASE WHEN tb.bet_type_id = ANY(ARRAY[2, 7]) THEN (tb.valid_bet * dr.rate) * tb.odds ELSE 0 END) AS sum_6
		FROM
			tbl_bets tb
		JOIN tbl_users tu ON tb.user_uuid = tu.uuid
		LEFT JOIN tbl_currencies_default_rates dr ON tb.currency_id = dr.currency_id
		WHERE
			tb.round_uuid = $1
		AND
			tu.username not ilike 'IT%'
	)
	SELECT
		type,
		CASE
			WHEN type = 1 THEN COALESCE(sum_1, 0)
			WHEN type = 2 THEN COALESCE(sum_2, 0)
			WHEN type = 3 THEN COALESCE(sum_3, 0)
			WHEN type = 4 THEN COALESCE(sum_4, 0)
			WHEN type = 5 THEN COALESCE(sum_5, 0)
			WHEN type = 6 THEN COALESCE(sum_6, 0)
		END AS total_pay
	FROM (SELECT generate_series(1, 6) AS type) t
	CROSS JOIN aggregated_bets;
	`
	betTypes := []BetsType{}
	err = db.Select(&betTypes, sql, round)
	if err != nil {
		sugar.Error(err)
		return rarityResult()
	}

	if len(betTypes) == 0 {
		return pkg.GenerateNaturalNumbers()
	}
	sugar.Infow("betTypes", "data", betTypes)

	// =====================Prevent player win more than total turnover=====================
	report, isDanger, err := companyMarginCalc(db, setting.Margin, betTypes)
	if err != nil {
		sugar.Error(err)
		return rarityResult()
	}

	filteredDangerBetTypes := []BetsType{}
	margin := setting.Margin
	companyMargin := report.TurnOver * float64(margin) / 100
	allowWin := report.WinLose - companyMargin
	pp.Println("margin", margin, "companyMargin", companyMargin, "allowWin", allowWin)
	pp.Println(report)
	if isDanger {
		for _, betType := range betTypes {
			if betType.TotalPay < allowWin {
				filteredDangerBetTypes = append(filteredDangerBetTypes, betType)
			}
		}
		if len(filteredDangerBetTypes) == 0 {
			sort.Slice(betTypes, func(i, j int) bool {
				return betTypes[i].TotalPay < betTypes[j].TotalPay
			})
			filteredDangerBetTypes = append(filteredDangerBetTypes, betTypes[0])
		}
		fmt.Println(`
		==================
		||  Is Danger   ||
		|| pick random  ||
		==================
		`)
		pp.Println(filteredDangerBetTypes)
		return GetResultByBetType(getRandomBetType(filteredDangerBetTypes).Type)
	} else {

		// If company win more than 50% of total turnover, system will help player to win
		// rand probability to help player win
		helpChance := rand.Float64()
		isHelp := helpChance < float64(setting.LowPay)/100
		if report.WinLose > 2*companyMargin && isHelp {
			for _, betType := range betTypes {
				if betType.TotalPay > 0 && betType.TotalPay < allowWin {
					filteredDangerBetTypes = append(filteredDangerBetTypes, betType)
				}
			}
			if len(filteredDangerBetTypes) == 0 {
				sort.Slice(betTypes, func(i, j int) bool {
					return betTypes[i].TotalPay < betTypes[j].TotalPay
				})
				filteredDangerBetTypes = append(filteredDangerBetTypes, betTypes[0])
			}
			fmt.Println(`
			==================
			||  help player ||
			|| pick random  ||
			==================
			`)
			pp.Println(filteredDangerBetTypes)
			return GetResultByBetType(getRandomBetType(filteredDangerBetTypes).Type)
		} else {
			// =====================Normal system pick=====================
			fmt.Println(`
			==================
			||    Normal    ||
			|| system pick  ||
			==================
			`)
			rand.Shuffle(len(betTypes), func(i, j int) { betTypes[i], betTypes[j] = betTypes[j], betTypes[i] })
			return GetResultByBetType(chooseBetTypeStrategy(betTypes, setting))
		}
	}

}

func companyMarginCalc(db *sqlx.DB, margin int, betTypes []BetsType) (*TotalReport, bool, error) {

	loc, _ := time.LoadLocation("Asia/Phnom_Penh")
	today := time.Now().In(loc).Format("2006-01-02")
	isDanger := false
	sql := `
		WITH converted_bets AS (
			SELECT
				bet_amount * ter.rate AS turnover,
				-tb.win_lost_amount * ter.rate AS winlose
			FROM
				tbl_bets tb
			JOIN
				tbl_currencies_default_rates ter ON tb.currency_id = ter.currency_id
			JOIN
				tbl_users tu ON tu.uuid = tb.user_uuid
			WHERE
				date(tb.created_at AT TIME ZONE 'Asia/Phnom_Penh') BETWEEN $1 AND $1
				AND tu.username NOT ILIKE 'IT%'
				AND ter.default_currency_id = 1
		)
		SELECT
			sum(cb.turnover) AS turn_over,
			SUM(cb.winlose) AS win_lose
		FROM
			converted_bets cb
	`
	report := TotalReport{}
	err := db.Get(&report, sql, today)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get company margin for date %s: %w", today, err)
	}

	companyMargin := (report.TurnOver) * float64(margin) / 100
	allowWin := report.WinLose - companyMargin
	for _, betType := range betTypes {

		if betType.TotalPay > allowWin {
			isDanger = true
			break
		}
	}

	return &report, isDanger, nil
}

func getRandomBetType(betTypes []BetsType) BetsType {
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	return betTypes[r.Intn(len(betTypes))]
}


func generateNaturalResult(){
	pkg.GenerateShuffledNumbersRange(0,50)
}
