package internal

import (
	"context"
	"encoding/json"
	"log"
	"os"
	"smartrpc/internal/gamecore"
	"smartrpc/logger"
	"smartrpc/pkg"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
	amqp "github.com/rabbitmq/amqp091-go"
)

type RoundPayload struct {
	Round string `json:"round"`
}

type ManualPayload struct {
	ResultID int `json:"result_id"`
}

func SmartResultRpc(conn *amqp.Connection, db *sqlx.DB, rdb *redis.Client) {
	logger := logger.GetLogger()
	sugar := logger.Sugar()
	queueName := os.Getenv("QUEUE_NAME")
	ch, err := conn.Channel()
	if err != nil {
		log.Fatalf("Failed to open a channel: %v", err)
	}
	defer ch.Close()

	q, err := ch.QueueDeclare(
		queueName, // name
		false,     // durable
		false,     // delete when unused
		true,      // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	pkg.FailOnError(err, "Failed to declare a queue")

	err = ch.Qos(
		1,     // prefetch count
		0,     // prefetch size
		false, // global
	)

	pkg.FailOnError(err, "Failed to set QoS")

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	pkg.FailOnError(err, "Failed to register a consumer")

	var forever chan struct{}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		for d := range msgs {
			// fmt.Printf("Received a message: %s\n", d.Body)
			roundUuid := RoundPayload{}
			json.Unmarshal(d.Body, &roundUuid)

			sugar.Infof("Received a message: %s\n", roundUuid.Round)

			result := gamecore.SystemDecision(roundUuid.Round, db, rdb)
			sugar.Infof("Result: %v\n", result)

			stringifyResult, err := json.Marshal(result)
			pkg.FailOnError(err, "Failed to convert body to integer")

			err = ch.PublishWithContext(ctx,
				"",        // exchange
				d.ReplyTo, // routing key
				false,     // mandatory
				false,     // immediate
				amqp.Publishing{
					ContentType:   "application/json",
					CorrelationId: d.CorrelationId,
					Body:          []byte(stringifyResult),
				})
			pkg.FailOnError(err, "Failed to publish a message")

			d.Ack(false)
		}
	}()

	sugar.Infof(" [*] Waiting for messages. To exit press CTRL+C")
	<-forever

}

func ManualResultRpc(conn *amqp.Connection, db *sqlx.DB, rdb *redis.Client) {
	logger := logger.GetLogger()
	sugar := logger.Sugar()
	queueName := os.Getenv("MANUAL_QUEUE_NAME")
	ch, err := conn.Channel()
	if err != nil {
		log.Fatalf("Failed to open a channel: %v", err)
	}
	defer ch.Close()

	q, err := ch.QueueDeclare(
		queueName, // name
		false,     // durable
		false,     // delete when unused
		true,      // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	pkg.FailOnError(err, "Failed to declare a queue")

	err = ch.Qos(
		1,     // prefetch count
		0,     // prefetch size
		false, // global
	)

	pkg.FailOnError(err, "Failed to set QoS")

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	pkg.FailOnError(err, "Failed to register a consumer")

	var forever chan struct{}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		for d := range msgs {
			// fmt.Printf("Received a message: %s\n", d.Body)
			data := ManualPayload{}
			json.Unmarshal(d.Body, &data)

			sugar.Infof("manual message: %s", data.ResultID)

			result := gamecore.GetResultByBetType(data.ResultID)
			stringifyResult, err := json.Marshal(result)
			pkg.FailOnError(err, "Failed to convert body to integer")

			err = ch.PublishWithContext(ctx,
				"",        // exchange
				d.ReplyTo, // routing key
				false,     // mandatory
				false,     // immediate
				amqp.Publishing{
					ContentType:   "application/json",
					CorrelationId: d.CorrelationId,
					Body:          []byte(stringifyResult),
				})
			pkg.FailOnError(err, "Failed to publish a message")

			d.Ack(false)
		}
	}()

	sugar.Infof(" [*] Waiting for manuial messages. To exit press CTRL+C")
	<-forever

}
