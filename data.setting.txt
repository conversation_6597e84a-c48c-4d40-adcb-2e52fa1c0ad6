{"margin": 5, "low_pay": 20, "natural": 70, "lowest_payout": 5, "highest_payout": 5}

--margin: for company gain as percentage
--low_pay: a chance giving player a win without company lose
--natural: generate 1-80 and take 20s number as result
--lowest_payout:a chance of player losing money (if system can't avoid lose it will take the lowest payout as result type)
--highest_payout: a chance of player win the big amount of money. (system will not allow player to win more than allow win amount. allow win amount is calculated dynamicaly)

#Note
low_pay+natural+lowest_payout+highest_payout must be 100 no more no less. !margin not include
if not do that system won't work currectly
