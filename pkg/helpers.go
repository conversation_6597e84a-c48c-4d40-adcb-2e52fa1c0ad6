package pkg

import (
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/dustin/go-humanize"
)

func GenerateRandomNumbers() []int {
	numbers := make([]int, 80)
	for i := range numbers {
		numbers[i] = i + 1
	}
	rand.Shuffle(len(numbers), func(i, j int) { numbers[i], numbers[j] = numbers[j], numbers[i] })
	return numbers[:20]
}

func FailOnError(err error, msg string) {
	if err != nil {
		log.Panicf("%s: %s", msg, err)
	}
}

// ConvStrToSliceInt takes a comma-separated string of numbers and returns a slice of int.
//
// It returns an error if any of the numbers in the string are not valid integers.
func ConvStrToSliceInt(s string) ([]int, error) {
	var betNums []int
	for _, s := range strings.Split(s, ",") {
		if s == "" {
			continue
		}
		num, err := strconv.Atoi(s)
		if err != nil {
			return nil, fmt.Errorf("failed to parse '%s' as int: %w", s, err)
		}
		betNums = append(betNums, num)
	}
	return betNums, nil
}

// intSliceToString takes a slice of integers and a separator string and returns a single
// string with the elements of the slice joined by the separator.
//
// It is useful for converting a slice of integers to a string, such as for printing or
// storing in a database.
func intSliceToString(nums []int, separator string) string {
	// Create a slice to hold the string representations of the integers
	strNums := make([]string, len(nums))

	// Convert each integer to a string
	for i, num := range nums {
		strNums[i] = strconv.Itoa(num)
	}

	// Join the string slice with the separator
	return strings.Join(strNums, separator)
}

// PrintToBox prints a box with summary of the simulation.
//
// The box will contain the following information:
//   - Finished in: the time it took to finish the simulation
//   - Total bet amount: the total amount of money bet
//   - Win/Loss: the total amount of money won or lost
//   - Result: a comma-separated string of the numbers drawn in the simulation.
//     The first 10 numbers are on the first line, the rest are on the second line.
func PrintToBox(start time.Time, totalBetAmount, winlose float64, result []int) {
	boxWidth := 66
	horizontalLine := strings.Repeat("=", boxWidth)

	fmt.Println(horizontalLine)
	printCenteredLine("Summary", boxWidth)
	fmt.Println(horizontalLine)

	printKeyValueLine("Finished in", time.Since(start).String(), boxWidth)
	printKeyValueLine("Total bet amount", humanize.CommafWithDigits(totalBetAmount, 2), boxWidth)
	printKeyValueLine("Win/Loss", humanize.CommafWithDigits(winlose, 2), boxWidth)
	printKeyValueLine("Result", fmt.Sprintf("%v", intSliceToString(result[:10], ",")), boxWidth)
	printKeyValueLine("", fmt.Sprintf("%v", intSliceToString(result[10:], ",")), boxWidth)

	fmt.Println(horizontalLine)
}

func printCenteredLine(text string, width int) {
	padding := (width - len(text)) / 2
	fmt.Printf("%s%s%s\n", strings.Repeat(" ", padding), text, strings.Repeat(" ", width-padding-len(text)))
}

func printKeyValueLine(key string, value string, width int) {
	format := "| %-20s : %-" + strconv.Itoa(width-27) + "s |\n"
	fmt.Printf(format, key, value)
}
