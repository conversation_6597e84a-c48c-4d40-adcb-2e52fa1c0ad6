package pkg

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"strings"

	"github.com/lib/pq"
)

type IntArray []int

func (a *IntArray) Scan(src interface{}) error {
	switch src := src.(type) {
	case []byte:
		return a.scanBytes(src)
	case string:
		return a.scanBytes([]byte(src))
	case nil:
		*a = nil
		return nil
	default:
		return fmt.Errorf("unsupported type for IntArray: %T", src)
	}
}

func (a *IntArray) scanBytes(src []byte) error {
	str := string(src)

	// Remove the curly braces
	str = strings.Trim(str, "{}")

	if str == "" {
		*a = []int{}
		return nil
	}

	// Split the string by comma
	strNums := strings.Split(str, ",")

	nums := make([]int, len(strNums))
	for i, s := range strNums {
		num, err := strconv.Atoi(strings.TrimSpace(s))
		if err != nil {
			return fmt.Erro<PERSON>("failed to parse '%s' as int: %w", s, err)
		}
		nums[i] = num
	}

	*a = nums
	return nil
}

func (a IntArray) Value() (driver.Value, error) {
	return pq.Array(a).Value()
}
