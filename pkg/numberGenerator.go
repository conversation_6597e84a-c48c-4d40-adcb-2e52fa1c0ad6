package pkg

import (
	"fmt"
	"math/rand"
	"sort"
	"time"
)

type Rank struct {
	Number int
	Rank   int
}

// GenerateSuffledNumbers generates a slice of shuffled numbers from 1 to amount.
// Example:
//
//	numbers := GenerateSuffledNumbers(5)
//	fmt.Println(numbers) // [4 2 5 1 3]
func GenerateSuffledNumbers(amount int) []int {
	numbers := make([]int, amount)
	for i := range numbers {
		numbers[i] = i + 1
	}
	rand.Shuffle(len(numbers), func(i, j int) { numbers[i], numbers[j] = numbers[j], numbers[i] })
	return numbers
}

func PickUniqueNumbers() []int {
	numbers := GenerateSuffledNumbers(80)
	return numbers[:20]
}

func RemoveNumbersFromSlice(numbers []int, numsToRemove []int) []int {
	result := numbers
	for _, num := range numsToRemove {
		for i := len(result) - 1; i >= 0; i-- {
			if result[i] == num {
				result = append(result[:i], result[i+1:]...)
				break
			}
		}
	}
	return result
}

func GenerateNaturalNumbers() []int {
	// Create a new random source with current time nanosecond seed
	source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)

	// Generate permutation of numbers 0-79
	perm := rng.Perm(80)

	// Take first 20 numbers and convert to 1-80 range
	numbers := make([]int, 20)
	for i := 0; i < 20; i++ {
		numbers[i] = perm[i] + 1
	}

	return numbers

}

// RankingData takes a slice of slices of integers and returns a slice of Ranks.
//
// It does the following:
// 1. Create a map to store counts of each number
// 2. Iterate through each sub-slice
// 3. Count unique numbers in this slice
// 4. Convert the map to a slice of Ranks
// 5. Sort the results in descending order
//
// The returned slice of Ranks contains the number and its count.
// Only numbers that appear more than once are included.
func RankingData(slices [][]int) []Rank {
	// Create a map to store counts of each number
	countMap := make(map[int]int)

	// Iterate through each sub-slice
	for _, slice := range slices {
		// Create a temporary map to avoid counting duplicates in a single slice
		tempMap := make(map[int]bool)

		// Count unique numbers in this slice
		for _, num := range slice {
			if !tempMap[num] {
				countMap[num]++
				tempMap[num] = true
			}
		}
	}
	// Convert the map to a slice of Ranks
	results := make([]Rank, 0)
	for num, count := range countMap {
		// Only include numbers that appear more than once
		if count > 1 {
			results = append(results, Rank{Number: num, Rank: count})
		}
	}

	// Sort the results in descending order
	sort.Slice(results, func(i, j int) bool {
		return results[i].Rank > results[j].Rank
	})
	return results
}

// FindNumber finds the index of the target element in the given array.
// If the array is empty, it returns -1 and an error.
// If the target element is not found, it returns -1 and an error.
func FindNumber(arr []int, target int) (int, error) {
	if len(arr) == 0 {
		return -1, fmt.Errorf("array is empty")
	}
	for i, num := range arr {
		if num == target {
			return i, nil
		}
	}
	return -1, fmt.Errorf("%d not found in array", target)
}

// SumArray takes a slice of integers and returns the sum of all elements.
func SumArrayInt(arr []int) int {
	sum := 0
	for _, num := range arr {
		sum += num
	}
	return sum
}
