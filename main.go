package main

import (
	"context"
	"log"
	"os"
	"smartrpc/internal"
	"smartrpc/internal/rabitmq"
	"smartrpc/logger"
	"smartrpc/pkg"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
	"go.uber.org/zap"
)

func main() {
	// Load environment variables
	err := godotenv.Load()
	if err != nil {
		log.Printf("Error loading .env file")
	}

	// Initialize logger
	logger := logger.GetLogger()
	defer logger.Sync()

	nums := pkg.GenerateShuffledNumbersRange(0, 10)
	log.Println(nums)

	// Database connection
	db, err := sqlx.Connect("postgres", os.Getenv("DATABASE_URL"))
	if err != nil {
		logger.Fatal("failed to connect to database", zap.Error(err))
	}
	defer db.Close()

	// Redis connection
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
	defer rdb.Close()

	// Verify Redis connection
	ctx := context.Background()
	_, err = rdb.Ping(ctx).Result()
	if err != nil {
		logger.Fatal("failed to connect to redis", zap.Error(err))
	}

	// RabbitMQ connection manager
	rb := rabitmq.NewRabbitBackground(os.Getenv("AMQP_URI"))
	defer rb.Shutdown()

	// conn, err := rb.GetConnection(ctx)

	go func() {
		for range rb.IsReconnected {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			conn, err := rb.GetConnection(ctx)
			cancel()
			if err != nil {
				logger.Error("Failed to get connection: %v", zap.Error(err))
			}
			logger.Info("RabbitMQ reconnected")
			if conn == nil {
				logger.Error("Connection is nil after reconnection")
				continue
			}
			// Start the RPC server
			go internal.ManualResultRpc(conn, db, rdb)
			go internal.SmartResultRpc(conn, db, rdb)
		}
	}()

	// Block forever
	select {}
}
